# Environment variables
.env

# Chat data and processed files
data/chats/*
data/processed/*
data/vectorstore/*
data/cache/*

# Keep directory structure but ignore contents
!data/chats/.gitkeep
!data/processed/.gitkeep
!data/vectorstore/.gitkeep
!data/cache/.gitkeep

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
