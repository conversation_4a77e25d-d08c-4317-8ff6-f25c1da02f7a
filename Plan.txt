Here's a refined blueprint for your **open-source semantic chat search project**, now centered around **`Instructor-XL`** as the default embedding model.

---

# 🧩 Project Blueprint: `ChatWhiz` v2 — *Instructor Edition*

> “A local-first, LLM-optional tool for smart semantic search over chat logs using **instruction-tuned embeddings**.”

---

## 📦 Stack Overview

| Component       | Tech Used                                                             |
| --------------- | --------------------------------------------------------------------- |
| Embedding Model | [`hkunlp/instructor-xl`](https://huggingface.co/hkunlp/instructor-xl) |
| Vector Store    | FAISS (local)                                                         |
| Chunking        | Per message / sliding window                                          |
| Search Modes    | Semantic / BM25 / Hybrid                                              |
| Optional LLM    | GPT (API key), or Local via Ollama                                    |
| UI (optional)   | Streamlit (upload + ask)                                              |
| Data Storage    | Local filesystem (optionally encrypted)                               |

---

## 📁 Directory Structure

```
chatwhiz/
├── main.py                     # CLI interface
├── config.yaml                 # Settings and defaults
├── .env                        # API keys (ignored in git)
├── requirements.txt
├── README.md
├── /data/
│   ├── chats/                  # Raw chat files
│   ├── processed/              # Parsed chunks
│   ├── vectorstore/            # FAISS index
│   └── cache/                  # Precomputed embeddings
├── /modules/
│   ├── loader.py               # Chat parsers + chunking
│   ├── embedder.py             # Instructor encoder wrapper
│   ├── vector_store.py         # FAISS manager
│   ├── retriever.py            # Top-k similarity search
│   ├── llm.py                  # Optional RAG via GPT/local LLM
│   └── encryptor.py            # Optional AES file encrypter
└── /ui/
    └── streamlit_app.py        # Optional UI (drag-drop + search)
```

---

## 🧠 Instructor-Based Embedding (`embedder.py`)

```python
from InstructorEmbedding import INSTRUCTOR

class InstructorEmbedder:
	def __init__(self, model_name="hkunlp/instructor-xl", instruction="Represent the chat message for semantic search:"):
		self.model = INSTRUCTOR(model_name)
		self.instruction = instruction

	def encode(self, texts):
		return self.model.encode([[self.instruction, t] for t in texts])
```

---

## 🔍 Search Flow (`main.py`)

```python
1. Load config.yaml (model, instruction, chunk size)
2. Load or parse chat logs → chunk → deduplicate
3. Embed chunks using Instructor
4. Store/retrieve with FAISS
5. On query:
   - Embed query with instruction
   - Top-k vector search
   - (Optional) RAG with LLM using retrieved context
6. Print or show results
```

---

## 🔧 `config.yaml` (Example)

```yaml
embedding_model: "hkunlp/instructor-xl"
instruction: "Represent the chat message for semantic search:"
chunk_size: 3

llm_provider: "none"          # none / openai / ollama
openai_api_key: ""
store_encrypted: false

retrieval_mode: "semantic"    # semantic / bm25 / hybrid
top_k: 5
```

---

## ✅ CLI Usage Examples

```bash
# Local semantic search
python main.py --query "What did Ravi say about the refund?"

# Hybrid retrieval (BM25 + Instructor rerank)
python main.py --query "What was the outcome of the team meeting?" --mode hybrid

# With optional LLM response
python main.py --query "Summarize John and Alice's conversation." --mode rag-gpt
```

---

## 💾 Encryption Support (Optional)

Use `cryptography` to AES-encrypt chat files:

```bash
python main.py --encrypt data/chats/work_chat.json
python main.py --decrypt data/chats/work_chat.json.enc
```

---

## 🧠 Example Prompt for Instructor (RAG)

When passing context to GPT:

```python
prompt = f"""
Based on the following chat messages, answer the question below:

{retrieved_context}

Q: {user_query}
A:
"""
```

---

## 🛠️ `requirements.txt`

```txt
InstructorEmbedding
faiss-cpu
openai
pyyaml
cryptography
python-dotenv
streamlit    # optional
```

---

## 📖 README Highlights

> 🔍 **Powerful semantic search** using the state-of-the-art `Instructor-XL` model
> 🔐 **Local-first** — your chat data stays on your machine
> 🧠 **Smart** — supports hybrid BM25 + embedding + optional GPT responses
> 🔧 **Plug-and-play** with any chat source (WhatsApp, Discord, Slack, etc.)

---

## 🧠 Notes

* Instructor-XL gives you state-of-the-art quality with a single model — **no separate QA or reranker models needed**.
* You can let users toggle between **Instructor-XL** and a smaller fallback model like `Instructor-large` via CLI or config.
* Designed so **non-technical users** can run it with `streamlit run ui/streamlit_app.py`, while devs can tweak via CLI.

---

Would you like me to start building the actual `main.py` and `embedder.py` files? Or turn this into a public GitHub-ready scaffold?
