# 🔍 ChatWhiz - Semantic Chat Search Tool

> **A local-first, LLM-optional tool for smart semantic search over chat logs using instruction-tuned embeddings.**

ChatWhiz enables you to search through your chat history using state-of-the-art semantic understanding powered by the **Instructor-XL** model. Whether you're looking for specific conversations, topics, or insights from your chat data, ChatWhiz makes it easy and private.

## ✨ Features

- 🧠 **Powerful semantic search** using the state-of-the-art `Instructor-XL` model
- 🔐 **Local-first** — your chat data stays on your machine
- 🔍 **Multiple search modes** — semantic, BM25 keyword, and hybrid search
- 🤖 **Optional AI responses** via OpenAI GPT or local Ollama models
- 🔧 **Multi-format support** — WhatsApp, Discord, JSON, CSV chat exports
- 🛡️ **Encryption support** — optional AES encryption for sensitive chats
- 🖥️ **Dual interface** — CLI for power users, Streamlit web UI for everyone
- ⚡ **Smart chunking** — intelligent message grouping with deduplication

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd ChatWhiz

# Install dependencies
pip install -r requirements.txt

# Copy environment template
cp .env.template .env
# Edit .env with your API keys (optional)
```

### 2. Index Your Chat Data

```bash
# Index a single chat file
python main.py index data/chats/my_chat.txt

# Index all files in a directory
python main.py index data/chats/

# Index with custom chunking strategy
python main.py index data/chats/ --chunk-strategy conversation
```

### 3. Search Your Chats

```bash
# Basic semantic search
python main.py search "What did John say about the meeting?"

# Keyword search with BM25
python main.py search "project deadline" --mode bm25

# Hybrid search (best of both worlds)
python main.py search "team discussion" --mode hybrid

# Get AI-powered responses (requires LLM setup)
python main.py search "Summarize our conversation about the budget" --rag
```

### 4. Web Interface

```bash
# Launch Streamlit web interface
streamlit run ui/streamlit_app.py

# Open http://localhost:8501 in your browser
```

## 📁 Supported Chat Formats

| Format | Description | Example |
|--------|-------------|---------|
| **WhatsApp** | Text export from WhatsApp | `DD/MM/YYYY, HH:MM - Sender: Message` |
| **Discord** | JSON export from DiscordChatExporter | Full message objects with metadata |
| **JSON** | Generic JSON chat format | `[{"text": "...", "sender": "...", "timestamp": "..."}]` |
| **CSV** | Comma-separated values | Columns: `text`, `sender`, `timestamp` |

## ⚙️ Configuration

Edit `config.yaml` to customize ChatWhiz:

```yaml
# Embedding Model Settings
embedding_model: "hkunlp/instructor-xl"
instruction: "Represent the chat message for semantic search:"
chunk_size: 3

# LLM Provider (optional)
llm_provider: "openai"  # none / openai / ollama
openai_api_key: "your-key-here"

# Search Settings
retrieval_mode: "semantic"  # semantic / bm25 / hybrid
top_k: 5
similarity_threshold: 0.7

# Security
store_encrypted: false
```

## 🔐 Privacy & Security

### Local-First Design
- All processing happens on your machine
- No data sent to external services (except optional LLM calls)
- Full control over your chat data

### Encryption Support
```bash
# Encrypt sensitive chat files
python main.py encrypt data/chats/private_chat.json

# Decrypt when needed
python main.py decrypt data/chats/private_chat.json.enc

# Index encrypted files (auto-decrypt during processing)
python main.py index data/chats/ --decrypt
```

## 🤖 AI Integration (Optional)

### OpenAI GPT
1. Get an API key from [OpenAI](https://platform.openai.com/)
2. Add to `.env`: `OPENAI_API_KEY=your-key-here`
3. Update `config.yaml`: `llm_provider: "openai"`

### Local Ollama
1. Install [Ollama](https://ollama.ai/)
2. Pull a model: `ollama pull llama2`
3. Update `config.yaml`:
   ```yaml
   llm_provider: "ollama"
   ollama_model: "llama2"
   ollama_url: "http://localhost:11434"
   ```

## 📊 Search Modes

### Semantic Search
Uses Instructor-XL embeddings to find messages with similar meaning, even if they use different words.

**Example:** Query "meeting" finds "conference", "discussion", "gathering"

### BM25 Keyword Search
Traditional keyword-based search using TF-IDF scoring.

**Example:** Query "project deadline" finds exact keyword matches

### Hybrid Search
Combines semantic and keyword search for the best of both worlds.

**Example:** Balances meaning similarity with keyword relevance

## 🛠️ Advanced Usage

### Custom Chunking Strategies

```bash
# Fixed-size chunks (non-overlapping)
python main.py index data/ --chunk-strategy fixed

# Sliding window (overlapping for context)
python main.py index data/ --chunk-strategy sliding_window

# Conversation-based (natural breaks)
python main.py index data/ --chunk-strategy conversation
```

### Batch Operations

```bash
# Rebuild entire index
python main.py index data/ --rebuild

# Check system statistics
python main.py stats

# Process multiple file types
find data/chats -name "*.txt" -o -name "*.json" | xargs python main.py index
```

## 🏗️ Architecture

```
ChatWhiz/
├── main.py                 # CLI interface
├── config.yaml            # Configuration
├── requirements.txt        # Dependencies
├── /data/                  # Data storage
│   ├── chats/             # Raw chat files
│   ├── processed/         # Parsed chunks
│   ├── vectorstore/       # FAISS index
│   └── cache/             # Embedding cache
├── /modules/              # Core modules
│   ├── embedder.py        # Instructor-XL wrapper
│   ├── vector_store.py    # FAISS management
│   ├── loader.py          # Chat parsers
│   ├── retriever.py       # Search engine
│   ├── llm.py             # LLM integration
│   └── encryptor.py       # Encryption utilities
└── /ui/
    └── streamlit_app.py   # Web interface
```

## 🔧 Troubleshooting

### Common Issues

**"No module named 'InstructorEmbedding'"**
```bash
pip install InstructorEmbedding
```

**"FAISS not found"**
```bash
pip install faiss-cpu
# or for GPU support:
pip install faiss-gpu
```

**"Model download fails"**
- Ensure stable internet connection
- Model downloads ~1.3GB on first use
- Check available disk space

**"Search returns no results"**
- Verify data is indexed: `python main.py stats`
- Try different search modes
- Lower similarity threshold in config

### Performance Tips

- Use GPU if available for faster embedding
- Increase `chunk_size` for longer context
- Use `flat` index for small datasets, `ivf` for large ones
- Enable embedding cache for repeated searches

## 📝 License

This project is open source. See LICENSE file for details.

## 🤝 Contributing

Contributions welcome! Please read CONTRIBUTING.md for guidelines.

## 🙏 Acknowledgments

- [Instructor-XL](https://huggingface.co/hkunlp/instructor-xl) for state-of-the-art embeddings
- [FAISS](https://github.com/facebookresearch/faiss) for efficient similarity search
- [Streamlit](https://streamlit.io/) for the beautiful web interface

---

**Made with ❤️ for better chat search experiences**
