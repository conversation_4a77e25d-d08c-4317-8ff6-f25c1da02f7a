#!/usr/bin/env python3
"""
Check indexing progress
"""

import os
import sys
import time

def check_progress():
    """Check the current indexing progress"""
    
    # Check if vector store files exist
    vectorstore_files = [
        'data/vectorstore/default.index',
        'data/vectorstore/default_metadata.pkl',
        'data/vectorstore/default_config.json'
    ]
    
    bm25_files = [
        'data/processed/bm25_index.pkl',
        'data/processed/bm25_corpus.pkl',
        'data/processed/bm25_metadata.pkl'
    ]
    
    print("📊 ChatWhiz Indexing Progress")
    print("=" * 40)
    
    # Check vector store
    vector_complete = all(os.path.exists(f) for f in vectorstore_files)
    print(f"🔍 Semantic Index: {'✅ Complete' if vector_complete else '⏳ In Progress'}")
    
    if vector_complete:
        try:
            # Try to get file sizes
            index_size = os.path.getsize('data/vectorstore/default.index')
            print(f"   Index size: {index_size / (1024*1024):.1f} MB")
        except:
            pass
    
    # Check BM25
    bm25_complete = all(os.path.exists(f) for f in bm25_files)
    print(f"📝 BM25 Index: {'✅ Complete' if bm25_complete else '⏳ In Progress'}")
    
    # Check cache
    cache_files = len([f for f in os.listdir('data/cache') if f.endswith('.pkl')]) if os.path.exists('data/cache') else 0
    print(f"💾 Cache Files: {cache_files}")
    
    if vector_complete and bm25_complete:
        print("\n🎉 Indexing Complete! You can now search your WhatsApp chat.")
        print("\nTry these commands:")
        print('python main.py search "your search query"')
        print('python main.py stats')
        print('python run_ui.py')
        return True
    else:
        print("\n⏳ Indexing still in progress...")
        print("This may take several minutes for large chat files.")
        return False

if __name__ == "__main__":
    check_progress()
