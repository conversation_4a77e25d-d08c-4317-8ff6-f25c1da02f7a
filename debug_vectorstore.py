#!/usr/bin/env python3
"""
Debug vector store loading
"""

import sys
import os
import json
sys.path.append('modules')

from modules.vector_store import FAISSVectorStore

def debug_vectorstore():
    """Debug vector store loading"""
    print("🔍 Debugging Vector Store")
    print("=" * 30)
    
    # Check files
    files = [
        'data/vectorstore/default.index',
        'data/vectorstore/default_config.json', 
        'data/vectorstore/default_metadata.pkl'
    ]
    
    for file in files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file}: {size} bytes")
        else:
            print(f"❌ {file}: Missing")
    
    # Try to load config
    try:
        with open('data/vectorstore/default_config.json', 'r') as f:
            config = json.load(f)
        print(f"\n📋 Config: {config}")
    except Exception as e:
        print(f"❌ Config error: {e}")
        return
    
    # Try to create and load vector store
    try:
        store = FAISSVectorStore(config['dimension'])
        success = store.load('default')
        print(f"\n🔄 Load success: {success}")
        
        if success:
            stats = store.get_stats()
            print(f"📊 Stats: {stats}")
        else:
            print("❌ Failed to load vector store")
            
    except Exception as e:
        print(f"❌ Vector store error: {e}")

if __name__ == "__main__":
    debug_vectorstore()
