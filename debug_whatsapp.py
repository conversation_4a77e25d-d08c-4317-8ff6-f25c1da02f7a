#!/usr/bin/env python3
"""
Debug script for WhatsApp chat parsing issues.
"""

import os
import sys
import re
from datetime import datetime

# Add modules to path
sys.path.append('modules')
from modules.loader import <PERSON><PERSON><PERSON><PERSON><PERSON>

def analyze_whatsapp_file(file_path):
    """Analyze a WhatsApp file to understand its format."""
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    print(f"📁 Analyzing file: {file_path}")
    print(f"📏 File size: {os.path.getsize(file_path)} bytes")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='latin-1') as f:
                content = f.read()
            print("⚠️  File was encoded in latin-1, not UTF-8")
        except Exception as e:
            print(f"❌ Could not read file: {e}")
            return
    
    lines = content.split('\n')
    print(f"📄 Total lines: {len(lines)}")
    
    # Show first few lines
    print("\n📝 First 10 lines:")
    for i, line in enumerate(lines[:10], 1):
        print(f"{i:2d}: {repr(line)}")
    
    # Test different WhatsApp patterns
    patterns = {
        "Standard": r'(\d{1,2}/\d{1,2}/\d{4}), (\d{1,2}:\d{2}) - ([^:]+): (.+)',
        "Alternative 1": r'(\d{1,2}/\d{1,2}/\d{2}), (\d{1,2}:\d{2}) - ([^:]+): (.+)',
        "Alternative 2": r'(\d{4}-\d{2}-\d{2}), (\d{1,2}:\d{2}) - ([^:]+): (.+)',
        "Alternative 3": r'(\d{1,2}\.\d{1,2}\.\d{4}), (\d{1,2}:\d{2}) - ([^:]+): (.+)',
        "Alternative 4": r'\[(\d{1,2}/\d{1,2}/\d{4}), (\d{1,2}:\d{2}:\d{2})\] ([^:]+): (.+)',
        "Alternative 5": r'(\d{1,2}/\d{1,2}/\d{4}) (\d{1,2}:\d{2}) - ([^:]+): (.+)',
    }
    
    print("\n🔍 Testing patterns:")
    for name, pattern in patterns.items():
        matches = re.findall(pattern, content, re.MULTILINE)
        print(f"{name:15s}: {len(matches)} matches")
        if matches and len(matches) > 0:
            print(f"                 Example: {matches[0]}")
    
    # Try to parse with ChatLoader
    print("\n🤖 Testing with ChatLoader:")
    try:
        loader = ChatLoader()
        messages = loader.load_whatsapp_export(file_path)
        print(f"✅ ChatLoader found {len(messages)} messages")
        
        if messages:
            print("📋 First message:")
            msg = messages[0]
            print(f"   Sender: {msg.sender}")
            print(f"   Text: {msg.text}")
            print(f"   Time: {msg.timestamp}")
    except Exception as e:
        print(f"❌ ChatLoader failed: {e}")

def suggest_fixes(file_path):
    """Suggest fixes for common WhatsApp parsing issues."""
    print("\n💡 Troubleshooting suggestions:")
    print("1. Check date format - should be DD/MM/YYYY or MM/DD/YYYY")
    print("2. Check time format - should be HH:MM")
    print("3. Check separator - should be ' - ' (space-dash-space)")
    print("4. Check encoding - file should be UTF-8")
    print("5. Remove any header/footer text from WhatsApp export")
    print("\n📝 Expected format:")
    print("15/01/2024, 10:30 - John: Hello there!")
    print("15/01/2024, 10:31 - Jane: Hi John, how are you?")

def main():
    """Main function."""
    print("🔧 WhatsApp Chat Debug Tool")
    print("=" * 40)
    
    # Check for WhatsApp files in common locations
    possible_files = [
        "WhatsApp Chat with Johan.txt",
        "data/chats/WhatsApp Chat with Johan.txt",
        "WhatsApp Chat.txt"
    ]
    
    found_file = None
    for file_path in possible_files:
        if os.path.exists(file_path):
            found_file = file_path
            break
    
    if found_file:
        analyze_whatsapp_file(found_file)
        suggest_fixes(found_file)
    else:
        print("❌ No WhatsApp file found in expected locations:")
        for file_path in possible_files:
            print(f"   - {file_path}")
        print("\n💡 Please specify the correct file path:")
        print("python debug_whatsapp.py path/to/your/whatsapp/file.txt")
        
        # If user provided a file path as argument
        if len(sys.argv) > 1:
            file_path = sys.argv[1]
            analyze_whatsapp_file(file_path)
            suggest_fixes(file_path)

if __name__ == "__main__":
    main()
