#!/usr/bin/env python3
"""
Efficiently index the WhatsApp chat file in batches.
"""

import sys
import os
import yaml
import time

# Add modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

def index_whatsapp_efficiently():
    print("Indexing WhatsApp chat efficiently...")
    
    try:
        # Load config
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Import modules
        from modules.embedder import create_embedder_from_config
        from modules.vector_store import create_vector_store_from_config
        from modules.loader import ChatLoader
        from modules.retriever import ChatRetriever
        
        # Initialize system
        print("Initializing system...")
        embedder = create_embedder_from_config(config)
        dimension = embedder.get_embedding_dimension()
        vector_store = create_vector_store_from_config(config, dimension)
        retriever = ChatRetriever(embedder, vector_store, config.get('processed_dir', 'data/processed'))
        
        # Initialize loader
        loader = ChatLoader(chunk_size=config.get('chunk_size', 3))
        
        file_path = "data/chats/WhatsApp Chat with <PERSON>.txt"
        
        print(f"Processing file: {file_path}")
        print(f"File size: {os.path.getsize(file_path) / (1024*1024):.1f} MB")
        
        # Process in batches to avoid memory issues
        batch_size = 50000  # Process 50k lines at a time
        all_chunks = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        total_lines = len(lines)
        print(f"Total lines: {total_lines}")
        
        for start_idx in range(0, total_lines, batch_size):
            end_idx = min(start_idx + batch_size, total_lines)
            batch_lines = lines[start_idx:end_idx]
            
            print(f"Processing batch {start_idx//batch_size + 1}: lines {start_idx}-{end_idx}")
            
            # Write batch to temp file
            temp_file = f"temp_batch_{start_idx}.txt"
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.writelines(batch_lines)
            
            # Load messages from batch
            start_time = time.time()
            messages = loader.load_whatsapp_export(temp_file)
            end_time = time.time()
            
            print(f"  Loaded {len(messages)} messages in {end_time - start_time:.2f} seconds")
            
            if messages:
                # Create chunks
                chunks = loader.create_chunks(messages, strategy='sliding_window')
                print(f"  Created {len(chunks)} chunks")
                all_chunks.extend(chunks)
            
            # Clean up temp file
            os.remove(temp_file)
        
        print(f"\nTotal chunks created: {len(all_chunks)}")
        
        # Deduplicate chunks
        print("Deduplicating chunks...")
        unique_chunks = loader.deduplicate_chunks(all_chunks)
        print(f"After deduplication: {len(unique_chunks)} chunks")
        
        # Index chunks in smaller batches to avoid memory issues
        print("Indexing chunks in batches...")
        index_batch_size = 1000  # Process 1000 chunks at a time

        # Clear existing indices if rebuilding
        print("Clearing existing indices...")
        retriever.vector_store.clear()

        total_batches = (len(unique_chunks) + index_batch_size - 1) // index_batch_size

        for batch_idx in range(0, len(unique_chunks), index_batch_size):
            batch_end = min(batch_idx + index_batch_size, len(unique_chunks))
            batch_chunks = unique_chunks[batch_idx:batch_end]
            batch_num = batch_idx // index_batch_size + 1

            print(f"\nIndexing batch {batch_num}/{total_batches}: chunks {batch_idx}-{batch_end}")

            try:
                # Extract texts for this batch
                texts = [chunk.text for chunk in batch_chunks]

                # Generate embeddings for this batch
                print(f"  Generating embeddings for {len(texts)} texts...")
                embeddings = retriever.embedder.encode(texts, use_cache=True, batch_size=16)

                # Add to vector store
                print(f"  Adding to vector store...")
                texts = [chunk.text for chunk in batch_chunks]
                metadata = [chunk.get_metadata() for chunk in batch_chunks]
                retriever.vector_store.add_embeddings(embeddings, texts, metadata)

                print(f"  ✅ Batch {batch_num} complete!")

            except Exception as e:
                print(f"  ❌ Error in batch {batch_num}: {e}")
                continue

        # Save vector store
        print("\nSaving vector store...")
        retriever.vector_store.save("default")

        # Build BM25 index
        print("Building BM25 index...")
        retriever._build_bm25_index(unique_chunks)
        retriever._save_bm25_index()

        print("✅ Indexing complete!")

        # Test search
        print("\nTesting search for 'What did Devjith say about his nipples?'")
        results = retriever.search("What did Devjith say about his nipples?", mode='semantic', k=5, threshold=0.3)

        print(f"Found {len(results)} results:")
        for i, result in enumerate(results, 1):
            print(f"\n[Result {i}] Score: {result.score:.3f}")
            print(f"Text: {result.text}")
            if 'senders' in result.metadata:
                print(f"Participants: {', '.join(result.metadata['senders'])}")
            print("-" * 40)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    index_whatsapp_efficiently()
