#!/usr/bin/env python3
"""
ChatWhiz - Semantic Chat Search Tool
Main CLI interface for indexing and searching chat data.
"""

import os
import sys
import argparse
import yaml
from typing import List, Optional
from dotenv import load_dotenv

# Add modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from modules.embedder import create_embedder_from_config
from modules.vector_store import create_vector_store_from_config
from modules.loader import ChatLoader
from modules.retriever import ChatRetriever
from modules.llm import create_rag_system
from modules.encryptor import encrypt_chat_file, decrypt_chat_file, is_file_encrypted


def load_config(config_path: str = "config.yaml") -> dict:
    """Load configuration from YAML file."""
    if not os.path.exists(config_path):
        print(f"Config file not found: {config_path}")
        print("Using default configuration...")
        return {
            'embedding_model': 'hkunlp/instructor-xl',
            'instruction': 'Represent the chat message for semantic search:',
            'chunk_size': 3,
            'llm_provider': 'none',
            'retrieval_mode': 'semantic',
            'top_k': 5,
            'similarity_threshold': 0.7
        }
    
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)


def setup_system(config: dict):
    """Initialize the ChatWhiz system components."""
    print("Initializing ChatWhiz...")
    
    # Create embedder
    embedder = create_embedder_from_config(config)
    
    # Get embedding dimension
    print("Getting embedding dimension...")
    dimension = embedder.get_embedding_dimension()
    
    # Create vector store
    vector_store = create_vector_store_from_config(config, dimension)
    
    # Create retriever
    retriever = ChatRetriever(embedder, vector_store, config.get('processed_dir', 'data/processed'))
    
    # Create RAG system (optional)
    rag_system = create_rag_system(config)
    
    return embedder, vector_store, retriever, rag_system


def index_chat_files(args, config: dict):
    """Index chat files for search."""
    print(f"Indexing chat files from: {args.input}")
    
    # Initialize system
    embedder, vector_store, retriever, _ = setup_system(config)
    
    # Initialize loader
    loader = ChatLoader(chunk_size=config.get('chunk_size', 3))
    
    # Process files
    all_chunks = []
    
    if os.path.isfile(args.input):
        files = [args.input]
    elif os.path.isdir(args.input):
        files = []
        for root, _, filenames in os.walk(args.input):
            for filename in filenames:
                if filename.endswith(('.txt', '.json', '.csv')):
                    files.append(os.path.join(root, filename))
    else:
        print(f"Invalid input path: {args.input}")
        return
    
    print(f"Found {len(files)} chat files to process")
    
    for file_path in files:
        print(f"Processing: {file_path}")
        
        try:
            # Check if file is encrypted
            if is_file_encrypted(file_path):
                print(f"File appears to be encrypted: {file_path}")
                if args.decrypt:
                    decrypted_path = decrypt_chat_file(file_path)
                    file_path = decrypted_path
                else:
                    print("Skipping encrypted file. Use --decrypt to decrypt first.")
                    continue
            
            # Load messages
            messages = loader.auto_detect_and_load(file_path)
            print(f"Loaded {len(messages)} messages")
            
            # Create chunks
            chunks = loader.create_chunks(messages, strategy=args.chunk_strategy)
            print(f"Created {len(chunks)} chunks")
            
            all_chunks.extend(chunks)
            
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            continue
    
    if not all_chunks:
        print("No chunks to index!")
        return
    
    # Deduplicate chunks
    all_chunks = loader.deduplicate_chunks(all_chunks)
    
    # Index chunks
    retriever.index_chunks(all_chunks, rebuild=args.rebuild)
    
    print(f"Indexing complete! Indexed {len(all_chunks)} chunks.")


def search_chats(args, config: dict):
    """Search indexed chat data."""
    # Initialize system
    embedder, vector_store, retriever, rag_system = setup_system(config)
    
    # Check if indices exist
    stats = retriever.get_stats()
    if stats['semantic_vectors'] == 0 and stats['bm25_documents'] == 0:
        print("No indexed data found. Please run indexing first.")
        return
    
    print(f"Searching with mode: {args.mode}")
    print(f"Query: {args.query}")
    print("-" * 50)
    
    # Perform search
    try:
        search_kwargs = {'k': args.top_k}
        if args.mode == 'semantic':
            search_kwargs['threshold'] = config.get('similarity_threshold', 0.7)

        results = retriever.search(
            query=args.query,
            mode=args.mode,
            **search_kwargs
        )
        
        if not results:
            print("No results found.")
            return
        
        # Display results
        for i, result in enumerate(results, 1):
            print(f"\n[Result {i}] Score: {result.score:.3f} | Type: {result.search_type}")
            print(f"Text: {result.text}")
            
            # Show metadata if available
            metadata = result.metadata
            if 'senders' in metadata:
                print(f"Participants: {', '.join(metadata['senders'])}")
            if 'start_time' in metadata:
                print(f"Time: {metadata['start_time']}")
            
            print("-" * 40)
        
        # Generate RAG response if requested and available
        if args.rag and rag_system:
            print("\n" + "="*50)
            print("AI RESPONSE:")
            print("="*50)
            
            rag_result = rag_system.generate_answer(args.query, results)
            print(rag_result['answer'])
            
            if 'error' in rag_result:
                print(f"\nNote: {rag_result['error']}")
        
        elif args.rag and not rag_system:
            print("\nRAG response requested but no LLM provider configured.")
    
    except Exception as e:
        print(f"Search error: {e}")


def encrypt_file(args):
    """Encrypt a chat file."""
    try:
        output_path = encrypt_chat_file(args.file, output_path=args.output)
        print(f"File encrypted successfully: {output_path}")
    except Exception as e:
        print(f"Encryption error: {e}")


def decrypt_file(args):
    """Decrypt a chat file."""
    try:
        output_path = decrypt_chat_file(args.file, output_path=args.output)
        print(f"File decrypted successfully: {output_path}")
    except Exception as e:
        print(f"Decryption error: {e}")


def show_stats(config: dict):
    """Show system statistics."""
    try:
        embedder, vector_store, retriever, rag_system = setup_system(config)
        stats = retriever.get_stats()
        
        print("ChatWhiz System Statistics:")
        print("-" * 30)
        print(f"Semantic vectors: {stats['semantic_vectors']}")
        print(f"BM25 documents: {stats['bm25_documents']}")
        print(f"Embedding dimension: {stats['embedding_dimension']}")
        print(f"Index type: {stats['index_type']}")
        print(f"LLM provider: {config.get('llm_provider', 'none')}")
        
        if rag_system and rag_system.llm_provider.is_available():
            print("LLM status: Available")
        else:
            print("LLM status: Not available")
    
    except Exception as e:
        print(f"Error getting stats: {e}")


def main():
    """Main CLI interface."""
    # Load environment variables
    load_dotenv()
    
    parser = argparse.ArgumentParser(
        description="ChatWhiz - Semantic Chat Search Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Index chat files
  python main.py index data/chats/

  # Search with semantic similarity
  python main.py search "What did John say about the meeting?"

  # Search with hybrid mode
  python main.py search "project deadline" --mode hybrid

  # Search with AI response
  python main.py search "Summarize the discussion" --rag

  # Encrypt a chat file
  python main.py encrypt data/chats/private_chat.json

  # Show system statistics
  python main.py stats
        """
    )
    
    parser.add_argument('--config', default='config.yaml', help='Configuration file path')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Index command
    index_parser = subparsers.add_parser('index', help='Index chat files')
    index_parser.add_argument('input', help='Input file or directory')
    index_parser.add_argument('--chunk-strategy', choices=['fixed', 'sliding_window', 'conversation'], 
                             default='sliding_window', help='Chunking strategy')
    index_parser.add_argument('--rebuild', action='store_true', help='Rebuild existing indices')
    index_parser.add_argument('--decrypt', action='store_true', help='Decrypt encrypted files during indexing')
    
    # Search command
    search_parser = subparsers.add_parser('search', help='Search chat data')
    search_parser.add_argument('query', help='Search query')
    search_parser.add_argument('--mode', choices=['semantic', 'bm25', 'hybrid'], 
                              default='semantic', help='Search mode')
    search_parser.add_argument('--top-k', type=int, default=5, help='Number of results to return')
    search_parser.add_argument('--rag', action='store_true', help='Generate AI response using RAG')
    
    # Encrypt command
    encrypt_parser = subparsers.add_parser('encrypt', help='Encrypt a chat file')
    encrypt_parser.add_argument('file', help='File to encrypt')
    encrypt_parser.add_argument('--output', help='Output file path')
    
    # Decrypt command
    decrypt_parser = subparsers.add_parser('decrypt', help='Decrypt a chat file')
    decrypt_parser.add_argument('file', help='File to decrypt')
    decrypt_parser.add_argument('--output', help='Output file path')
    
    # Stats command
    stats_parser = subparsers.add_parser('stats', help='Show system statistics')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Load configuration
    config = load_config(args.config)
    
    # Override config with command line args where applicable
    if hasattr(args, 'mode'):
        config['retrieval_mode'] = args.mode
    if hasattr(args, 'top_k'):
        config['top_k'] = args.top_k
    
    # Execute command
    try:
        if args.command == 'index':
            index_chat_files(args, config)
        elif args.command == 'search':
            search_chats(args, config)
        elif args.command == 'encrypt':
            encrypt_file(args)
        elif args.command == 'decrypt':
            decrypt_file(args)
        elif args.command == 'stats':
            show_stats(config)
    
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
    except Exception as e:
        print(f"Error: {e}")
        if '--debug' in sys.argv:
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()
