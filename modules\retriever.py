"""
Retrieval system for ChatWhiz supporting semantic, BM25, and hybrid search modes.
"""

import os
import pickle
from typing import List, Tuple, Dict, Any, Optional
import numpy as np
from rank_bm25 import BM25Oka<PERSON>

from .embedder import InstructorEmbedder
from .vector_store import FAISSVectorStore
from .loader import ChatChunk


class SearchResult:
    """Represents a search result with score and metadata."""
    
    def __init__(
        self,
        text: str,
        score: float,
        metadata: Dict[str, Any],
        search_type: str = "semantic"
    ):
        self.text = text
        self.score = score
        self.metadata = metadata
        self.search_type = search_type
    
    def __repr__(self):
        return f"SearchResult(score={self.score:.3f}, type={self.search_type})"


class ChatRetriever:
    """
    Multi-modal retrieval system supporting semantic, BM25, and hybrid search.
    """
    
    def __init__(
        self,
        embedder: InstructorEmbedder,
        vector_store: FAISSVectorStore,
        store_dir: str = "data/processed"
    ):
        """
        Initialize the retriever.
        
        Args:
            embedder: Instructor embedder for semantic search
            vector_store: FAISS vector store
            store_dir: Directory to store BM25 index and other data
        """
        self.embedder = embedder
        self.vector_store = vector_store
        self.store_dir = store_dir
        self.bm25_index = None
        self.bm25_corpus = []
        self.bm25_metadata = []
        
        os.makedirs(store_dir, exist_ok=True)
    
    def index_chunks(self, chunks: List[ChatChunk], rebuild: bool = False, batch_size: int = 1000):
        """
        Index chat chunks for both semantic and BM25 search with batch processing.

        Args:
            chunks: List of chat chunks to index
            rebuild: Whether to rebuild existing indices
            batch_size: Number of chunks to process in each batch
        """
        if not chunks:
            print("No chunks to index")
            return

        print(f"Indexing {len(chunks)} chunks in batches of {batch_size}...")

        # Index for semantic search
        if rebuild or self.vector_store.get_stats()['total_vectors'] == 0:
            print("Creating semantic embeddings...")

            if rebuild:
                self.vector_store.clear()

            # Process in batches to avoid memory issues
            total_batches = (len(chunks) + batch_size - 1) // batch_size

            for batch_idx in range(0, len(chunks), batch_size):
                batch_end = min(batch_idx + batch_size, len(chunks))
                batch_chunks = chunks[batch_idx:batch_end]
                batch_num = batch_idx // batch_size + 1

                print(f"  Processing batch {batch_num}/{total_batches}: {len(batch_chunks)} chunks")

                # Extract texts and metadata for this batch
                texts = [chunk.text for chunk in batch_chunks]
                metadata = [chunk.get_metadata() for chunk in batch_chunks]

                # Generate embeddings for this batch
                embeddings = self.embedder.encode(texts, use_cache=True, batch_size=16)

                # Add to vector store using the correct method
                self.vector_store.add_embeddings(embeddings, texts, metadata)

                print(f"    Added {len(batch_chunks)} vectors to index")

            # Save vector store
            print("Saving vector store...")
            self.vector_store.save("default")

        # Index for BM25 search
        if rebuild or self.bm25_index is None:
            print("Creating BM25 index...")
            texts = [chunk.text for chunk in chunks]
            metadata = [chunk.get_metadata() for chunk in chunks]
            self._create_bm25_index(texts, metadata)
            self._save_bm25_index()

        print("Indexing completed!")
    
    def _create_bm25_index(self, texts: List[str], metadata: List[Dict[str, Any]]):
        """Create BM25 index from texts."""
        # Tokenize texts (simple whitespace tokenization)
        tokenized_corpus = [text.lower().split() for text in texts]
        
        self.bm25_index = BM25Okapi(tokenized_corpus)
        self.bm25_corpus = texts
        self.bm25_metadata = metadata
    
    def _save_bm25_index(self):
        """Save BM25 index to disk."""
        bm25_path = os.path.join(self.store_dir, "bm25_index.pkl")
        corpus_path = os.path.join(self.store_dir, "bm25_corpus.pkl")
        metadata_path = os.path.join(self.store_dir, "bm25_metadata.pkl")
        
        with open(bm25_path, 'wb') as f:
            pickle.dump(self.bm25_index, f)
        
        with open(corpus_path, 'wb') as f:
            pickle.dump(self.bm25_corpus, f)
        
        with open(metadata_path, 'wb') as f:
            pickle.dump(self.bm25_metadata, f)
        
        print("BM25 index saved")
    
    def _load_bm25_index(self) -> bool:
        """Load BM25 index from disk."""
        bm25_path = os.path.join(self.store_dir, "bm25_index.pkl")
        corpus_path = os.path.join(self.store_dir, "bm25_corpus.pkl")
        metadata_path = os.path.join(self.store_dir, "bm25_metadata.pkl")
        
        if not all(os.path.exists(p) for p in [bm25_path, corpus_path, metadata_path]):
            return False
        
        try:
            with open(bm25_path, 'rb') as f:
                self.bm25_index = pickle.load(f)
            
            with open(corpus_path, 'rb') as f:
                self.bm25_corpus = pickle.load(f)
            
            with open(metadata_path, 'rb') as f:
                self.bm25_metadata = pickle.load(f)
            
            print("BM25 index loaded")
            return True
        
        except Exception as e:
            print(f"Error loading BM25 index: {e}")
            return False
    
    def semantic_search(
        self,
        query: str,
        k: int = 5,
        threshold: float = 0.7
    ) -> List[SearchResult]:
        """
        Perform semantic search using embeddings.
        
        Args:
            query: Search query
            k: Number of results to return
            threshold: Minimum similarity threshold
            
        Returns:
            List of search results
        """
        # Load vector store if needed
        if self.vector_store.get_stats()['total_vectors'] == 0:
            if not self.vector_store.load("default"):
                print("No semantic index found. Please index some data first.")
                return []
        
        # Get query embedding
        query_embedding = self.embedder.encode_query(query)
        
        # Search
        results = self.vector_store.search(query_embedding, k, threshold)
        
        # Convert to SearchResult objects
        search_results = []
        for text, score, metadata in results:
            search_results.append(SearchResult(
                text=text,
                score=score,
                metadata=metadata,
                search_type="semantic"
            ))
        
        return search_results
    
    def bm25_search(
        self,
        query: str,
        k: int = 5
    ) -> List[SearchResult]:
        """
        Perform BM25 keyword search.
        
        Args:
            query: Search query
            k: Number of results to return
            
        Returns:
            List of search results
        """
        # Load BM25 index if needed
        if self.bm25_index is None:
            if not self._load_bm25_index():
                print("No BM25 index found. Please index some data first.")
                return []
        
        # Tokenize query
        query_tokens = query.lower().split()
        
        # Get BM25 scores
        scores = self.bm25_index.get_scores(query_tokens)
        
        # Get top-k results
        top_indices = np.argsort(scores)[::-1][:k]
        
        search_results = []
        for idx in top_indices:
            if scores[idx] > 0:  # Only include results with positive scores
                search_results.append(SearchResult(
                    text=self.bm25_corpus[idx],
                    score=float(scores[idx]),
                    metadata=self.bm25_metadata[idx],
                    search_type="bm25"
                ))
        
        return search_results
    
    def hybrid_search(
        self,
        query: str,
        k: int = 5,
        semantic_weight: float = 0.7,
        bm25_weight: float = 0.3,
        semantic_threshold: float = 0.5
    ) -> List[SearchResult]:
        """
        Perform hybrid search combining semantic and BM25 results.
        
        Args:
            query: Search query
            k: Number of results to return
            semantic_weight: Weight for semantic scores
            bm25_weight: Weight for BM25 scores
            semantic_threshold: Minimum semantic similarity threshold
            
        Returns:
            List of search results ranked by combined score
        """
        # Get results from both methods
        semantic_results = self.semantic_search(query, k * 2, semantic_threshold)
        bm25_results = self.bm25_search(query, k * 2)
        
        # Normalize scores to [0, 1] range
        if semantic_results:
            max_semantic = max(r.score for r in semantic_results)
            for result in semantic_results:
                result.score = result.score / max_semantic if max_semantic > 0 else 0
        
        if bm25_results:
            max_bm25 = max(r.score for r in bm25_results)
            for result in bm25_results:
                result.score = result.score / max_bm25 if max_bm25 > 0 else 0
        
        # Combine results
        combined_scores = {}
        
        # Add semantic results
        for result in semantic_results:
            chunk_id = result.metadata.get('chunk_id', result.text[:50])
            combined_scores[chunk_id] = {
                'result': result,
                'semantic_score': result.score,
                'bm25_score': 0.0
            }
        
        # Add BM25 results
        for result in bm25_results:
            chunk_id = result.metadata.get('chunk_id', result.text[:50])
            if chunk_id in combined_scores:
                combined_scores[chunk_id]['bm25_score'] = result.score
            else:
                combined_scores[chunk_id] = {
                    'result': result,
                    'semantic_score': 0.0,
                    'bm25_score': result.score
                }
        
        # Calculate combined scores
        final_results = []
        for chunk_id, data in combined_scores.items():
            combined_score = (
                semantic_weight * data['semantic_score'] +
                bm25_weight * data['bm25_score']
            )
            
            result = data['result']
            result.score = combined_score
            result.search_type = "hybrid"
            final_results.append(result)
        
        # Sort by combined score and return top-k
        final_results.sort(key=lambda x: x.score, reverse=True)
        return final_results[:k]
    
    def search(
        self,
        query: str,
        mode: str = "semantic",
        k: int = 5,
        **kwargs
    ) -> List[SearchResult]:
        """
        Unified search interface.
        
        Args:
            query: Search query
            mode: Search mode ('semantic', 'bm25', 'hybrid')
            k: Number of results to return
            **kwargs: Additional arguments for specific search modes
            
        Returns:
            List of search results
        """
        if mode == "semantic":
            return self.semantic_search(query, k, **kwargs)
        elif mode == "bm25":
            return self.bm25_search(query, k, **kwargs)
        elif mode == "hybrid":
            return self.hybrid_search(query, k, **kwargs)
        else:
            raise ValueError(f"Unknown search mode: {mode}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about indexed data."""
        # Try to load vector store if it's empty
        if self.vector_store.get_stats()['total_vectors'] == 0:
            self.vector_store.load("default")

        vector_stats = self.vector_store.get_stats()

        bm25_count = 0
        if self.bm25_index is not None:
            bm25_count = len(self.bm25_corpus)
        elif self._load_bm25_index():
            bm25_count = len(self.bm25_corpus)

        return {
            'semantic_vectors': vector_stats['total_vectors'],
            'bm25_documents': bm25_count,
            'embedding_dimension': vector_stats['dimension'],
            'index_type': vector_stats['index_type']
        }
