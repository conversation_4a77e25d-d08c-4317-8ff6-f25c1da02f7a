#!/usr/bin/env python3
"""
Monitor indexing progress
"""

import time
import os

def monitor_progress():
    """Monitor the indexing progress"""
    print("📊 Monitoring ChatWhiz Indexing Progress")
    print("Press Ctrl+C to stop monitoring")
    print("=" * 50)
    
    try:
        while True:
            # Check vector store size
            index_file = 'data/vectorstore/default.index'
            if os.path.exists(index_file):
                size_mb = os.path.getsize(index_file) / (1024 * 1024)
                print(f"🔍 Vector store size: {size_mb:.1f} MB")
            else:
                print("🔍 Vector store: Not created yet")
            
            # Check cache files
            cache_dir = 'data/cache'
            if os.path.exists(cache_dir):
                cache_files = len([f for f in os.listdir(cache_dir) if f.endswith('.pkl')])
                print(f"💾 Cache files: {cache_files}")
            
            print("-" * 30)
            time.sleep(10)  # Check every 10 seconds
            
    except KeyboardInterrupt:
        print("\n👋 Monitoring stopped")

if __name__ == "__main__":
    monitor_progress()
