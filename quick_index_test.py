#!/usr/bin/env python3
"""
Quick test to index a portion of the WhatsApp chat and test search.
"""

import sys
import os
import yaml
import time

# Add modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

def quick_index_test():
    print("Quick indexing test...")
    
    try:
        # Load config
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Import modules
        from modules.embedder import create_embedder_from_config
        from modules.vector_store import create_vector_store_from_config
        from modules.loader import ChatLoader
        from modules.retriever import ChatRetriever
        
        # Initialize system
        print("Initializing system...")
        embedder = create_embedder_from_config(config)
        dimension = embedder.get_embedding_dimension()
        vector_store = create_vector_store_from_config(config, dimension)
        retriever = ChatRetriever(embedder, vector_store, config.get('processed_dir', 'data/processed'))
        
        # Initialize loader
        loader = ChatLoader(chunk_size=config.get('chunk_size', 3))
        
        file_path = "data/chats/WhatsApp Chat with Johan.txt"
        
        # Process only the section with the nipples content (around line 82211)
        print("Processing section with nipples content...")
        with open(file_path, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            # Get lines around the nipples message (82000-83000)
            lines = all_lines[82000:83000]
        
        # Write to temp file
        temp_file = "temp_nipples_section.txt"
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        # Load messages
        print("Loading messages...")
        messages = loader.load_whatsapp_export(temp_file)
        print(f"Loaded {len(messages)} messages")
        
        # Create chunks
        print("Creating chunks...")
        chunks = loader.create_chunks(messages, strategy='sliding_window')
        print(f"Created {len(chunks)} chunks")
        
        # Index chunks
        print("Indexing chunks...")
        retriever.index_chunks(chunks, rebuild=True, batch_size=100)
        
        # Clean up temp file
        os.remove(temp_file)
        
        print("✅ Indexing complete!")
        
        # Test searches
        test_queries = [
            "What did Devjith say about his nipples?",
            "nipples",
            "Devjith nipples",
            "woman white cowboy hat",
            "Devjith"
        ]
        
        for query in test_queries:
            print(f"\n{'='*50}")
            print(f"Testing search: '{query}'")
            print('='*50)
            
            # Try semantic search
            results = retriever.search(query, mode='semantic', k=5, threshold=0.1)
            print(f"Semantic search found {len(results)} results:")
            
            for i, result in enumerate(results, 1):
                print(f"\n[Result {i}] Score: {result.score:.3f}")
                print(f"Text: {result.text}")
                if 'senders' in result.metadata:
                    print(f"Participants: {', '.join(result.metadata['senders'])}")
                print("-" * 30)
            
            # Try BM25 search
            results_bm25 = retriever.search(query, mode='bm25', k=5)
            print(f"\nBM25 search found {len(results_bm25)} results:")
            
            for i, result in enumerate(results_bm25, 1):
                print(f"\n[BM25 Result {i}] Score: {result.score:.3f}")
                print(f"Text: {result.text}")
                if 'senders' in result.metadata:
                    print(f"Participants: {', '.join(result.metadata['senders'])}")
                print("-" * 30)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_index_test()
