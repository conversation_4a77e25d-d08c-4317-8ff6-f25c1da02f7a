#!/usr/bin/env python3
"""
Quick test to verify WhatsApp parsing is working
"""

import sys
sys.path.append('modules')

from modules.loader import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_whatsapp_parsing():
    """Test WhatsApp parsing with the fixed loader"""
    print("🧪 Testing WhatsApp parsing...")
    
    loader = ChatLoader(chunk_size=3)
    
    # Load a small sample
    messages = loader.load_whatsapp_export("data/chats/WhatsApp Chat with Johan.txt")
    
    print(f"✅ Loaded {len(messages)} messages")
    
    if messages:
        print("\n📋 Sample messages:")
        for i, msg in enumerate(messages[:5]):
            print(f"{i+1}. [{msg.timestamp}] {msg.sender}: {msg.text[:50]}...")
        
        # Test chunking
        chunks = loader.create_chunks(messages[:20], strategy="sliding_window")
        print(f"\n📦 Created {len(chunks)} chunks from first 20 messages")
        
        if chunks:
            print(f"📝 Sample chunk:\n{chunks[0].text[:200]}...")
    
    return len(messages)

if __name__ == "__main__":
    count = test_whatsapp_parsing()
    print(f"\n🎉 Successfully parsed {count:,} WhatsApp messages!")
