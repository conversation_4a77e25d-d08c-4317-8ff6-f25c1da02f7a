#!/usr/bin/env python3
"""
Launcher script for ChatWhiz Streamlit UI.
This ensures the app runs from the correct directory with proper imports.
"""

import os
import sys
import subprocess

def main():
    # Get the directory where this script is located (should be ChatWhiz root)
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Change to the script directory
    os.chdir(script_dir)
    
    # Add the current directory to Python path
    sys.path.insert(0, script_dir)
    
    print("🚀 Starting ChatWhiz Web Interface...")
    print(f"📁 Working directory: {script_dir}")
    print("🌐 Opening http://localhost:8501")
    print("⏹️  Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        # Run streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "ui/streamlit_app.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 ChatWhiz UI stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running Streamlit: {e}")
        print("💡 Make sure Streamlit is installed: pip install streamlit")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
