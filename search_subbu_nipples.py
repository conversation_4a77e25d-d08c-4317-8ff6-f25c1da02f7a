#!/usr/bin/env python3
"""
Search for the specific "subbu 3 nipples" message.
"""

import sys
import os
import yaml

# Add modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

def search_subbu_message():
    print("Searching for <PERSON>bu's 3 nipples message...")
    
    try:
        # Load config and initialize system
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)

        from modules.embedder import create_embedder_from_config
        from modules.vector_store import create_vector_store_from_config
        from modules.retriever import ChatRetriever

        embedder = create_embedder_from_config(config)
        dimension = embedder.get_embedding_dimension()
        vector_store = create_vector_store_from_config(config, dimension)
        retriever = ChatRetriever(embedder, vector_store, config.get('processed_dir', 'data/processed'))

        # Search for the specific content
        queries = [
            'subbu 3 nipples',
            'subbu nipples',
            'i have 3 nipples',
            'subbu hehe',
            'subbu body parts',
            'three nipples',
            'subbu said about his body'
        ]

        for query in queries:
            print(f'\n=== Searching for: "{query}" ===')
            
            # Semantic search
            results = retriever.search(query, mode='semantic', k=5, threshold=0.1)
            print(f'Semantic search found {len(results)} results:')
            for i, result in enumerate(results, 1):
                print(f'[{i}] Score: {result.score:.3f}')
                print(f'Text: {result.text}')
                if 'senders' in result.metadata:
                    print(f'Participants: {", ".join(result.metadata["senders"])}')
                print('-' * 50)
            
            # BM25 search
            bm25_results = retriever.search(query, mode='bm25', k=5)
            print(f'\nBM25 search found {len(bm25_results)} results:')
            for i, result in enumerate(bm25_results, 1):
                print(f'[BM25-{i}] Score: {result.score:.3f}')
                print(f'Text: {result.text}')
                if 'senders' in result.metadata:
                    print(f'Participants: {", ".join(result.metadata["senders"])}')
                print('-' * 50)
        
        # Also search the raw file for this content
        print(f'\n{"="*60}')
        print("SEARCHING RAW FILE FOR 'subbu' and 'nipples'...")
        print("="*60)
        
        file_path = "data/chats/WhatsApp Chat with Johan.txt"
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Search for lines containing both subbu and nipples
            matching_lines = []
            for i, line in enumerate(lines):
                if 'subbu' in line.lower() and 'nipple' in line.lower():
                    matching_lines.append((i+1, line.strip()))
            
            print(f"Found {len(matching_lines)} lines with 'subbu' and 'nipples':")
            for line_num, line in matching_lines:
                print(f"Line {line_num}: {line}")
            
            # Search for just "3 nipples"
            nipples_3_lines = []
            for i, line in enumerate(lines):
                if '3 nipple' in line.lower():
                    nipples_3_lines.append((i+1, line.strip()))
            
            print(f"\nFound {len(nipples_3_lines)} lines with '3 nipples':")
            for line_num, line in nipples_3_lines:
                print(f"Line {line_num}: {line}")
                # Show context (previous and next lines)
                if i > 0:
                    print(f"  Previous: {lines[i-1].strip()}")
                if i < len(lines) - 1:
                    print(f"  Next: {lines[i+1].strip()}")
                print()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    search_subbu_message()
