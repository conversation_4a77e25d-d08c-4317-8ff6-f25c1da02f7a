#!/usr/bin/env python3
"""
Basic test script for ChatWhiz core functionality
"""

import sys
import os
sys.path.append('modules')

def test_encryption():
    """Test encryption functionality"""
    print("Testing encryption...")
    try:
        from modules.encryptor import ChatE<PERSON>ryptor
        
        # Test text encryption
        encryptor = ChatEncryptor("testpassword123")
        test_text = "Hello, this is a test message!"
        
        encrypted = encryptor.encrypt_text(test_text)
        print(f"Encrypted text: {encrypted[:50]}...")
        
        decrypted = encryptor.decrypt_text(encrypted)
        print(f"Decrypted text: {decrypted}")
        
        assert decrypted == test_text, "Decryption failed!"
        print("✅ Encryption test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Encryption test failed: {e}")
        return False

def test_chat_loading():
    """Test chat loading functionality"""
    print("\nTesting chat loading...")
    try:
        from modules.loader import Chat<PERSON>oader
        
        loader = ChatLoader(chunk_size=3)
        
        # Test loading the sample chat
        messages = loader.auto_detect_and_load("data/chats/sample_chat.json")
        print(f"Loaded {len(messages)} messages")
        
        # Test chunking
        chunks = loader.create_chunks(messages, strategy="sliding_window")
        print(f"Created {len(chunks)} chunks")
        
        # Test deduplication
        unique_chunks = loader.deduplicate_chunks(chunks)
        print(f"After deduplication: {len(unique_chunks)} chunks")
        
        print("✅ Chat loading test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Chat loading test failed: {e}")
        return False

def test_vector_store():
    """Test vector store functionality"""
    print("\nTesting vector store...")
    try:
        import numpy as np
        from modules.vector_store import FAISSVectorStore
        
        # Create a simple vector store
        dimension = 128
        store = FAISSVectorStore(dimension)
        
        # Add some dummy embeddings
        embeddings = np.random.rand(5, dimension).astype(np.float32)
        texts = [f"Test message {i}" for i in range(5)]
        metadata = [{"id": i} for i in range(5)]
        
        store.add_embeddings(embeddings, texts, metadata)
        
        # Test search
        query_embedding = np.random.rand(dimension).astype(np.float32)
        results = store.search(query_embedding, k=3)
        
        print(f"Found {len(results)} search results")
        print("✅ Vector store test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Vector store test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Running ChatWhiz Basic Tests")
    print("=" * 40)
    
    tests = [
        test_encryption,
        test_chat_loading,
        test_vector_store
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 40)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! ChatWhiz core functionality is working.")
    else:
        print("⚠️  Some tests failed. Check the output above.")

if __name__ == "__main__":
    main()
