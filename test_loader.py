#!/usr/bin/env python3
"""
Test the ChatLoader with the WhatsApp file to see what's causing the hang.
"""

import sys
import os
import time

# Add modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

def test_whatsapp_loading():
    print("Testing WhatsApp file loading...")
    
    try:
        from modules.loader import ChatLoader
        
        loader = ChatLoader(chunk_size=3)
        file_path = "data/chats/WhatsApp Chat with Johan.txt"
        
        print(f"File size: {os.path.getsize(file_path) / (1024*1024):.1f} MB")
        
        # Test with a small sample first
        print("Testing with first 1000 lines...")
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()[:1000]
        
        # Write sample to temp file
        temp_file = "temp_sample.txt"
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        start_time = time.time()
        messages = loader.load_whatsapp_export(temp_file)
        end_time = time.time()
        
        print(f"✅ Loaded {len(messages)} messages from sample in {end_time - start_time:.2f} seconds")
        
        if messages:
            print("Sample messages:")
            for i, msg in enumerate(messages[:3]):
                print(f"  {i+1}. [{msg.timestamp}] {msg.sender}: {msg.text[:50]}...")
        
        # Clean up
        os.remove(temp_file)
        
        # Now test with larger sample
        print("\nTesting with first 10000 lines...")
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()[:10000]
        
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        start_time = time.time()
        messages = loader.load_whatsapp_export(temp_file)
        end_time = time.time()
        
        print(f"✅ Loaded {len(messages)} messages from larger sample in {end_time - start_time:.2f} seconds")
        
        # Clean up
        os.remove(temp_file)
        
        # Search for nipples content in the messages
        nipples_messages = [msg for msg in messages if 'nipple' in msg.text.lower()]
        print(f"Found {len(nipples_messages)} messages containing 'nipple'")

        for msg in nipples_messages:
            print(f"  - [{msg.timestamp}] {msg.sender}: {msg.text}")

        # Test with a sample around line 82211 where the Devjith nipples message is
        print("\nTesting with lines around 82000-83000 (where nipples content is)...")
        with open(file_path, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            lines = all_lines[82000:83000]  # Get lines around the nipples message

        with open(temp_file, 'w', encoding='utf-8') as f:
            f.writelines(lines)

        start_time = time.time()
        messages = loader.load_whatsapp_export(temp_file)
        end_time = time.time()

        print(f"✅ Loaded {len(messages)} messages from nipples section in {end_time - start_time:.2f} seconds")

        # Search for nipples content in this section
        nipples_messages = [msg for msg in messages if 'nipple' in msg.text.lower()]
        print(f"Found {len(nipples_messages)} messages containing 'nipple' in this section")

        for msg in nipples_messages:
            print(f"  - [{msg.timestamp}] {msg.sender}: {msg.text}")

        # Also search for Devjith messages
        devjith_messages = [msg for msg in messages if msg.sender == 'Devjith Manish']
        print(f"Found {len(devjith_messages)} messages from Devjith in this section")

        for msg in devjith_messages[:5]:  # Show first 5
            print(f"  - [{msg.timestamp}] {msg.sender}: {msg.text}")

        # Clean up
        os.remove(temp_file)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_whatsapp_loading()
