#!/usr/bin/env python3
"""
Simple test script to debug search functionality.
"""

import sys
import os
import yaml

# Add modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

def test_search():
    print("Testing search functionality...")
    
    try:
        # Load config
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        print("✅ Config loaded")
        
        # Import modules
        from modules.embedder import create_embedder_from_config
        from modules.vector_store import create_vector_store_from_config
        from modules.retriever import ChatRetriever
        print("✅ Modules imported")
        
        # Create embedder
        print("Creating embedder...")
        embedder = create_embedder_from_config(config)
        print("✅ Embedder created")
        
        # Get dimension
        print("Getting embedding dimension...")
        dimension = embedder.get_embedding_dimension()
        print(f"✅ Dimension: {dimension}")
        
        # Create vector store
        print("Creating vector store...")
        vector_store = create_vector_store_from_config(config, dimension)
        print("✅ Vector store created")
        
        # Create retriever
        print("Creating retriever...")
        retriever = ChatRetriever(embedder, vector_store, config.get('processed_dir', 'data/processed'))
        print("✅ Retriever created")
        
        # Get stats
        print("Getting stats...")
        stats = retriever.get_stats()
        print(f"✅ Stats: {stats}")
        
        if stats['semantic_vectors'] == 0:
            print("❌ No semantic vectors found!")
            return
        
        # Test search with very low threshold
        query = "What did Devjith say about his nipples?"
        print(f"Searching for: '{query}'")

        results = retriever.search(query, mode='semantic', k=10, threshold=0.1)
        print(f"✅ Search completed. Found {len(results)} results")

        for i, result in enumerate(results, 1):
            print(f"\n[Result {i}] Score: {result.score:.3f}")
            print(f"Text: {result.text}")
            if 'senders' in result.metadata:
                print(f"Participants: {', '.join(result.metadata['senders'])}")
            print("-" * 40)

        # Also try searching for just "nipples"
        print(f"\n{'='*50}")
        print("Searching for just 'nipples':")
        results2 = retriever.search("nipples", mode='semantic', k=10, threshold=0.1)
        print(f"Found {len(results2)} results")

        for i, result in enumerate(results2, 1):
            print(f"\n[Nipples Result {i}] Score: {result.score:.3f}")
            print(f"Text: {result.text}")
            if 'senders' in result.metadata:
                print(f"Participants: {', '.join(result.metadata['senders'])}")
            print("-" * 40)

        # Try BM25 search as well
        print(f"\n{'='*50}")
        print("Trying BM25 search for 'Devjith nipples':")
        results3 = retriever.search("Devjith nipples", mode='bm25', k=10)
        print(f"Found {len(results3)} results")

        for i, result in enumerate(results3, 1):
            print(f"\n[BM25 Result {i}] Score: {result.score:.3f}")
            print(f"Text: {result.text}")
            if 'senders' in result.metadata:
                print(f"Participants: {', '.join(result.metadata['senders'])}")
            print("-" * 40)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_search()
