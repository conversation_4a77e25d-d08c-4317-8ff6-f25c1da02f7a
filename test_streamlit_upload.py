#!/usr/bin/env python3
"""
Test Streamlit upload functionality
"""

import os
import sys
import tempfile

# Add modules to path
sys.path.append('modules')

from modules.loader import ChatLoader
from modules.encryptor import is_file_encrypted

def test_upload_simulation():
    """Simulate the upload process"""
    print("🧪 Testing Upload Simulation")
    print("=" * 30)
    
    # Test with the small sample file
    test_file = "data/chats/small_sample.txt"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return
    
    print(f"📁 Testing with: {test_file}")
    
    # Read file content (simulating uploaded file)
    with open(test_file, 'rb') as f:
        file_content = f.read()
    
    print(f"📏 File size: {len(file_content)} bytes")
    
    # Create temporary file (simulating Streamlit upload)
    with tempfile.NamedTemporaryFile(delete=False, suffix="_test_upload.txt") as tmp_file:
        tmp_file.write(file_content)
        tmp_path = tmp_file.name
    
    print(f"💾 Temp file: {tmp_path}")
    
    try:
        # Test encryption detection
        is_encrypted = is_file_encrypted(tmp_path)
        print(f"🔐 Encrypted: {is_encrypted}")
        
        if is_encrypted:
            print("❌ File detected as encrypted (this shouldn't happen)")
            return
        
        # Test loading
        loader = ChatLoader(chunk_size=3)
        messages = loader.auto_detect_and_load(tmp_path)
        print(f"📨 Messages loaded: {len(messages)}")
        
        if messages:
            print("📋 Sample messages:")
            for i, msg in enumerate(messages[:3]):
                print(f"  {i+1}. [{msg.timestamp}] {msg.sender}: {msg.text[:50]}...")
            
            # Test chunking
            chunks = loader.create_chunks(messages, strategy='sliding_window')
            print(f"📦 Chunks created: {len(chunks)}")
            
            # Test deduplication
            unique_chunks = loader.deduplicate_chunks(chunks)
            print(f"🔄 After deduplication: {len(unique_chunks)}")
            
            print("✅ Upload simulation successful!")
        else:
            print("❌ No messages found")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        if os.path.exists(tmp_path):
            os.unlink(tmp_path)
            print(f"🗑️ Cleaned up temp file")

if __name__ == "__main__":
    test_upload_simulation()
