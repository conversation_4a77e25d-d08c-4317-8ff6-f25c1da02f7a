"""
Streamlit web interface for ChatWhiz.
Provides drag-and-drop file upload and interactive search functionality.
"""

import os
import sys
import tempfile
import streamlit as st
import yaml
from typing import List, Optional
import pandas as pd

# Add modules to path
current_dir = os.path.dirname(__file__)
parent_dir = os.path.dirname(current_dir)
modules_dir = os.path.join(parent_dir, 'modules')
sys.path.insert(0, parent_dir)
sys.path.insert(0, modules_dir)

try:
    from modules.embedder import create_embedder_from_config
    from modules.vector_store import create_vector_store_from_config
    from modules.loader import ChatLoader
    from modules.retriever import ChatRetriever
    from modules.llm import create_rag_system
    from modules.encryptor import is_file_encrypted, decrypt_chat_file
except ImportError as e:
    st.error(f"Import error: {e}")
    st.error("Please make sure you're running this from the ChatWhiz root directory")
    st.stop()


@st.cache_resource
def load_config():
    """Load configuration with caching."""
    # Try multiple possible config paths
    possible_paths = [
        'config.yaml',
        os.path.join('..', 'config.yaml'),
        os.path.join(os.path.dirname(__file__), '..', 'config.yaml')
    ]

    for config_path in possible_paths:
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    return yaml.safe_load(f)
            except Exception as e:
                st.warning(f"Error loading config from {config_path}: {e}")
                continue

    # Default config if none found
    return {
        'embedding_model': 'hkunlp/instructor-large',
        'instruction': 'Represent the chat message for semantic search:',
        'chunk_size': 3,
        'llm_provider': 'none',
        'retrieval_mode': 'semantic',
        'top_k': 5,
        'similarity_threshold': 0.7,
        'data_dir': 'data',
        'chats_dir': 'data/chats',
        'processed_dir': 'data/processed',
        'vectorstore_dir': 'data/vectorstore',
        'cache_dir': 'data/cache'
    }


@st.cache_resource
def initialize_system():
    """Initialize ChatWhiz system with caching."""
    config = load_config()
    
    with st.spinner("Initializing ChatWhiz system..."):
        # Create embedder
        embedder = create_embedder_from_config(config)
        
        # Get embedding dimension
        dimension = embedder.get_embedding_dimension()
        
        # Create vector store
        vector_store = create_vector_store_from_config(config, dimension)
        
        # Create retriever
        retriever = ChatRetriever(
            embedder, 
            vector_store, 
            config.get('processed_dir', 'data/processed')
        )
        
        # Create RAG system (optional)
        rag_system = create_rag_system(config)
        
        return config, embedder, vector_store, retriever, rag_system


def process_uploaded_files(uploaded_files, config):
    """Process uploaded chat files."""
    loader = ChatLoader(chunk_size=config.get('chunk_size', 3))
    all_chunks = []
    
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    for i, uploaded_file in enumerate(uploaded_files):
        status_text.text(f"Processing {uploaded_file.name}...")
        
        try:
            # Save uploaded file temporarily
            with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{uploaded_file.name}") as tmp_file:
                tmp_file.write(uploaded_file.getvalue())
                tmp_path = tmp_file.name
            
            # Check if encrypted
            if is_file_encrypted(tmp_path):
                st.warning(f"File {uploaded_file.name} appears to be encrypted. Skipping.")
                os.unlink(tmp_path)
                continue
            
            # Load messages
            messages = loader.auto_detect_and_load(tmp_path)
            
            if not messages:
                st.warning(f"No messages found in {uploaded_file.name}")
                os.unlink(tmp_path)
                continue
            
            # Create chunks
            chunks = loader.create_chunks(messages, strategy='sliding_window')
            all_chunks.extend(chunks)
            
            st.success(f"Processed {uploaded_file.name}: {len(messages)} messages, {len(chunks)} chunks")
            
            # Clean up
            os.unlink(tmp_path)
            
        except Exception as e:
            st.error(f"Error processing {uploaded_file.name}: {e}")
            if 'tmp_path' in locals():
                os.unlink(tmp_path)
        
        progress_bar.progress((i + 1) / len(uploaded_files))
    
    status_text.text("Processing complete!")
    
    if all_chunks:
        # Deduplicate
        all_chunks = loader.deduplicate_chunks(all_chunks)
        st.info(f"Total chunks after deduplication: {len(all_chunks)}")
    
    return all_chunks


def display_search_results(results, show_metadata=True):
    """Display search results in a nice format."""
    if not results:
        st.info("No results found.")
        return
    
    for i, result in enumerate(results, 1):
        with st.expander(f"Result {i} - Score: {result.score:.3f} ({result.search_type})"):
            st.write("**Message:**")
            st.write(result.text)
            
            if show_metadata and result.metadata:
                st.write("**Metadata:**")
                metadata_df = pd.DataFrame([result.metadata])
                st.dataframe(metadata_df, use_container_width=True)


def main():
    """Main Streamlit application."""
    st.set_page_config(
        page_title="ChatWhiz - Semantic Chat Search",
        page_icon="🔍",
        layout="wide"
    )
    
    st.title("🔍 ChatWhiz - Semantic Chat Search")
    st.markdown("*Search your chat history with AI-powered semantic understanding*")
    
    # Initialize system
    try:
        config, embedder, vector_store, retriever, rag_system = initialize_system()
    except Exception as e:
        st.error(f"Failed to initialize system: {e}")
        st.stop()
    
    # Sidebar for configuration
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        # Show current config
        st.subheader("Current Settings")
        st.write(f"**Model:** {config.get('embedding_model', 'N/A')}")
        st.write(f"**Chunk Size:** {config.get('chunk_size', 'N/A')}")
        st.write(f"**LLM Provider:** {config.get('llm_provider', 'none')}")
        
        # Show system stats
        st.subheader("System Statistics")
        try:
            stats = retriever.get_stats()
            st.metric("Semantic Vectors", stats['semantic_vectors'])
            st.metric("BM25 Documents", stats['bm25_documents'])
            st.metric("Embedding Dimension", stats['embedding_dimension'])
        except Exception as e:
            st.error(f"Error getting stats: {e}")
    
    # Main interface tabs
    tab1, tab2, tab3 = st.tabs(["🔍 Search", "📁 Upload & Index", "📊 Analytics"])
    
    with tab1:
        st.header("Search Your Chats")
        
        # Search interface
        col1, col2 = st.columns([3, 1])
        
        with col1:
            query = st.text_input(
                "Enter your search query:",
                placeholder="What did John say about the meeting?",
                help="Enter natural language queries to search your chat history"
            )
        
        with col2:
            search_mode = st.selectbox(
                "Search Mode:",
                ["semantic", "bm25", "hybrid"],
                help="Semantic: AI-powered similarity, BM25: Keyword matching, Hybrid: Combined approach"
            )
        
        # Search options
        col3, col4, col5 = st.columns(3)
        
        with col3:
            top_k = st.slider("Number of results:", 1, 20, 5)
        
        with col4:
            use_rag = st.checkbox(
                "Generate AI Response", 
                value=False,
                disabled=rag_system is None,
                help="Use LLM to generate a response based on search results"
            )
        
        with col5:
            show_metadata = st.checkbox("Show Metadata", value=True)
        
        # Search button
        if st.button("🔍 Search", type="primary") and query:
            with st.spinner("Searching..."):
                try:
                    results = retriever.search(
                        query=query,
                        mode=search_mode,
                        k=top_k,
                        threshold=config.get('similarity_threshold', 0.7)
                    )
                    
                    if results:
                        st.success(f"Found {len(results)} results")
                        
                        # Display results
                        display_search_results(results, show_metadata)
                        
                        # Generate RAG response if requested
                        if use_rag and rag_system:
                            st.markdown("---")
                            st.subheader("🤖 AI Response")
                            
                            with st.spinner("Generating AI response..."):
                                rag_result = rag_system.generate_answer(query, results)
                                
                                if 'error' in rag_result:
                                    st.error(f"Error generating response: {rag_result['error']}")
                                else:
                                    st.write(rag_result['answer'])
                                    
                                    with st.expander("Response Details"):
                                        st.write(f"Context used: {rag_result['context_used']} messages")
                                        st.write(f"Prompt length: {rag_result.get('prompt_length', 'N/A')} characters")
                    else:
                        st.info("No results found. Try adjusting your query or search mode.")
                
                except Exception as e:
                    st.error(f"Search error: {e}")
    
    with tab2:
        st.header("Upload & Index Chat Files")
        
        # File upload
        uploaded_files = st.file_uploader(
            "Choose chat files to upload:",
            type=['txt', 'json', 'csv'],
            accept_multiple_files=True,
            help="Supported formats: WhatsApp exports (.txt), JSON chat logs, CSV files"
        )
        
        if uploaded_files:
            st.write(f"Selected {len(uploaded_files)} files:")
            for file in uploaded_files:
                st.write(f"- {file.name} ({file.size} bytes)")
            
            # Processing options
            col1, col2 = st.columns(2)
            
            with col1:
                chunk_strategy = st.selectbox(
                    "Chunking Strategy:",
                    ["sliding_window", "fixed", "conversation"],
                    help="How to split messages into chunks for embedding"
                )
            
            with col2:
                rebuild_index = st.checkbox(
                    "Rebuild Existing Index",
                    help="Clear existing data and rebuild from scratch"
                )
            
            # Process button
            if st.button("📁 Process & Index Files", type="primary"):
                # Update config with selected strategy
                config['chunk_strategy'] = chunk_strategy
                
                # Process files
                chunks = process_uploaded_files(uploaded_files, config)
                
                if chunks:
                    with st.spinner("Indexing chunks..."):
                        try:
                            retriever.index_chunks(chunks, rebuild=rebuild_index)
                            st.success(f"Successfully indexed {len(chunks)} chunks!")
                            
                            # Clear cache to refresh stats
                            st.cache_resource.clear()
                            st.rerun()
                            
                        except Exception as e:
                            st.error(f"Indexing error: {e}")
                else:
                    st.warning("No chunks to index.")
    
    with tab3:
        st.header("Analytics & Insights")
        
        try:
            stats = retriever.get_stats()
            
            if stats['semantic_vectors'] > 0 or stats['bm25_documents'] > 0:
                # Display metrics
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("Total Vectors", stats['semantic_vectors'])
                
                with col2:
                    st.metric("BM25 Documents", stats['bm25_documents'])
                
                with col3:
                    st.metric("Embedding Dim", stats['embedding_dimension'])
                
                with col4:
                    st.metric("Index Type", stats['index_type'])
                
                # System status
                st.subheader("System Status")
                
                status_data = {
                    "Component": ["Embedder", "Vector Store", "BM25 Index", "LLM Provider"],
                    "Status": [
                        "✅ Ready" if embedder else "❌ Not Available",
                        "✅ Ready" if stats['semantic_vectors'] > 0 else "⚠️ Empty",
                        "✅ Ready" if stats['bm25_documents'] > 0 else "⚠️ Empty",
                        "✅ Available" if rag_system and rag_system.llm_provider.is_available() else "❌ Not Available"
                    ]
                }
                
                st.dataframe(pd.DataFrame(status_data), use_container_width=True)
                
                # Configuration display
                st.subheader("Current Configuration")
                config_df = pd.DataFrame([
                    {"Setting": k, "Value": str(v)} 
                    for k, v in config.items()
                ])
                st.dataframe(config_df, use_container_width=True)
                
            else:
                st.info("No data indexed yet. Upload some chat files to see analytics.")
        
        except Exception as e:
            st.error(f"Error loading analytics: {e}")
    
    # Footer
    st.markdown("---")
    st.markdown(
        "Built with ❤️ using [Instructor-XL](https://huggingface.co/hkunlp/instructor-xl) "
        "and [Streamlit](https://streamlit.io/)"
    )


if __name__ == "__main__":
    main()
