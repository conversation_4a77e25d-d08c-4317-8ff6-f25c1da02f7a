"""
Streamlit web interface for ChatWhiz.
Provides drag-and-drop file upload and interactive search functionality.
"""

import os
import sys
import tempfile
import streamlit as st
import yaml
from typing import List, Optional
import pandas as pd
import numpy as np

# Add modules to path
current_dir = os.path.dirname(__file__)
parent_dir = os.path.dirname(current_dir)
modules_dir = os.path.join(parent_dir, 'modules')
sys.path.insert(0, parent_dir)
sys.path.insert(0, modules_dir)

try:
    from modules.embedder import create_embedder_from_config
    from modules.vector_store import create_vector_store_from_config
    from modules.loader import ChatLoader
    from modules.retriever import ChatRetriever
    from modules.llm import create_rag_system
    from modules.encryptor import is_file_encrypted, decrypt_chat_file
except ImportError as e:
    st.error(f"Import error: {e}")
    st.error("Please make sure you're running this from the ChatWhiz root directory")
    st.stop()


@st.cache_resource
def load_config():
    """Load configuration with caching."""
    # Try multiple possible config paths
    possible_paths = [
        'config.yaml',
        os.path.join('..', 'config.yaml'),
        os.path.join(os.path.dirname(__file__), '..', 'config.yaml')
    ]

    for config_path in possible_paths:
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    return yaml.safe_load(f)
            except Exception as e:
                st.warning(f"Error loading config from {config_path}: {e}")
                continue

    # Default config if none found
    return {
        'embedding_model': 'hkunlp/instructor-large',
        'instruction': 'Represent the chat message for semantic search:',
        'device': 'auto',
        'chunk_size': 3,
        'llm_provider': 'none',
        'retrieval_mode': 'semantic',
        'top_k': 5,
        'similarity_threshold': 0.7,
        'data_dir': 'data',
        'chats_dir': 'data/chats',
        'processed_dir': 'data/processed',
        'vectorstore_dir': 'data/vectorstore',
        'cache_dir': 'data/cache'
    }


@st.cache_resource
def initialize_system():
    """Initialize ChatWhiz system with caching."""
    config = load_config()
    
    with st.spinner("Initializing ChatWhiz system..."):
        # Create embedder
        embedder = create_embedder_from_config(config)
        
        # Get embedding dimension
        dimension = embedder.get_embedding_dimension()
        
        # Create vector store
        vector_store = create_vector_store_from_config(config, dimension)
        
        # Create retriever
        retriever = ChatRetriever(
            embedder, 
            vector_store, 
            config.get('processed_dir', 'data/processed')
        )
        
        # Create RAG system (optional)
        rag_system = create_rag_system(config)
        
        return config, embedder, vector_store, retriever, rag_system


def process_uploaded_files(uploaded_files, config):
    """Process uploaded chat files with detailed progress tracking."""
    loader = ChatLoader(chunk_size=config.get('chunk_size', 3))
    all_chunks = []

    # Create progress containers
    main_progress = st.progress(0)
    status_container = st.container()

    with status_container:
        status_text = st.empty()
        file_progress = st.empty()
        details_expander = st.expander("📊 Processing Details", expanded=True)

    total_messages = 0
    total_chunks = 0

    for i, uploaded_file in enumerate(uploaded_files):
        current_status = f"📁 Processing file {i+1}/{len(uploaded_files)}: {uploaded_file.name}"
        status_text.text(current_status)

        with details_expander:
            st.write(f"**Current File:** {uploaded_file.name}")
            st.write(f"**File Size:** {uploaded_file.size:,} bytes")

        try:
            # Save uploaded file temporarily
            with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{uploaded_file.name}") as tmp_file:
                tmp_file.write(uploaded_file.getvalue())
                tmp_path = tmp_file.name

            # Check if encrypted
            if is_file_encrypted(tmp_path):
                st.warning(f"🔒 File {uploaded_file.name} appears to be encrypted. Skipping.")
                os.unlink(tmp_path)
                continue

            # Load messages with progress
            with details_expander:
                st.write("🔄 Loading messages...")

            messages = loader.auto_detect_and_load(tmp_path)

            if not messages:
                st.warning(f"⚠️ No messages found in {uploaded_file.name}")
                os.unlink(tmp_path)
                continue

            total_messages += len(messages)

            # Create chunks with progress
            with details_expander:
                st.write(f"✅ Loaded {len(messages):,} messages")
                st.write("📦 Creating chunks...")

            chunks = loader.create_chunks(messages, strategy=config.get('chunk_strategy', 'sliding_window'))
            all_chunks.extend(chunks)
            total_chunks += len(chunks)

            with details_expander:
                st.write(f"✅ Created {len(chunks):,} chunks")
                st.success(f"✅ Completed {uploaded_file.name}: {len(messages):,} messages → {len(chunks):,} chunks")

            # Clean up
            os.unlink(tmp_path)

        except Exception as e:
            st.error(f"❌ Error processing {uploaded_file.name}: {e}")
            if 'tmp_path' in locals():
                os.unlink(tmp_path)

        # Update main progress
        main_progress.progress((i + 1) / len(uploaded_files))

    status_text.text("🔄 Finalizing processing...")

    if all_chunks:
        # Deduplicate with progress
        with details_expander:
            st.write("🔄 Deduplicating chunks...")

        unique_chunks = loader.deduplicate_chunks(all_chunks)
        duplicates_removed = len(all_chunks) - len(unique_chunks)

        with details_expander:
            st.write(f"✅ Removed {duplicates_removed:,} duplicate chunks")
            st.info(f"📊 **Final Summary:**\n"
                   f"- Total messages: {total_messages:,}\n"
                   f"- Total chunks: {len(all_chunks):,}\n"
                   f"- Unique chunks: {len(unique_chunks):,}\n"
                   f"- Duplicates removed: {duplicates_removed:,}")

        status_text.text("✅ File processing complete!")
        return unique_chunks
    else:
        status_text.text("⚠️ No chunks created")
        return []


def index_chunks_with_progress(retriever, chunks, rebuild, progress_bar, status_text, details_container):
    """Index chunks with detailed progress tracking and encoding visualization."""
    import time

    total_chunks = len(chunks)
    status_text.text(f"🚀 Starting indexing process for {total_chunks:,} chunks...")

    with details_container:
        st.write(f"**Total chunks to process:** {total_chunks:,}")
        st.write(f"**Rebuild index:** {'Yes' if rebuild else 'No'}")

        # Show encoding model info
        model_info = retriever.embedder.get_model_info()
        st.write("**🧠 Embedding Model:**")
        st.write(f"- Model: {model_info['model_name']}")
        st.write(f"- Device: {model_info['device']}")
        st.write(f"- Dimension: {model_info['embedding_dimension']}")

    # Phase 1: Generate embeddings
    status_text.text("🧠 Phase 1: Generating embeddings...")

    with details_container:
        st.write("---")
        st.write("**🧠 Embedding Generation Phase**")
        embedding_progress = st.progress(0)
        embedding_status = st.empty()
        batch_info = st.empty()

    start_time = time.time()

    # Extract texts for embedding
    texts = [chunk.text for chunk in chunks]

    # Generate embeddings in batches with progress
    batch_size = 32  # Match the default batch size
    all_embeddings = []

    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i + batch_size]
        batch_num = i // batch_size + 1
        total_batches = (len(texts) + batch_size - 1) // batch_size

        embedding_status.text(f"Processing batch {batch_num}/{total_batches} ({len(batch_texts)} texts)")

        with batch_info:
            st.write(f"**Current Batch:** {batch_num}/{total_batches}")
            st.write(f"**Batch size:** {len(batch_texts)} texts")
            st.write(f"**Progress:** {i + len(batch_texts)}/{len(texts)} texts processed")

        # Generate embeddings for this batch
        batch_embeddings = retriever.embedder.encode(batch_texts, use_cache=True, batch_size=batch_size)
        all_embeddings.append(batch_embeddings)

        # Update progress
        progress = (i + len(batch_texts)) / len(texts)
        embedding_progress.progress(progress)
        progress_bar.progress(progress * 0.7)  # 70% of total progress for embedding

    embedding_time = time.time() - start_time

    with details_container:
        st.success(f"✅ Embeddings generated in {embedding_time:.2f} seconds")
        st.write(f"**Average time per text:** {embedding_time/len(texts):.3f} seconds")
        st.write(f"**Texts per second:** {len(texts)/embedding_time:.1f}")

    # Phase 2: Index in vector store
    status_text.text("📚 Phase 2: Building vector index...")

    with details_container:
        st.write("---")
        st.write("**📚 Vector Store Indexing Phase**")
        vector_progress = st.progress(0)
        vector_status = st.empty()

    # Combine embeddings
    import numpy as np
    embeddings = np.vstack(all_embeddings) if len(all_embeddings) > 1 else all_embeddings[0]

    # Index in vector store
    vector_status.text("Building FAISS index...")
    retriever.vector_store.clear() if rebuild else None

    # Add all embeddings at once using the correct method
    texts = [chunk.text for chunk in chunks]
    metadata = [chunk.get_metadata() for chunk in chunks]
    retriever.vector_store.add_embeddings(embeddings, texts, metadata)

    vector_progress.progress(1.0)
    vector_status.text(f"Indexed {len(chunks)} vectors")

    # Save vector store
    retriever.vector_store.save("default")
    progress_bar.progress(0.85)  # 85% complete

    # Phase 3: Build BM25 index
    status_text.text("🔍 Phase 3: Building BM25 index...")

    with details_container:
        st.write("---")
        st.write("**🔍 BM25 Index Building Phase**")
        bm25_status = st.empty()

    bm25_status.text("Building BM25 index...")
    retriever._build_bm25_index(chunks)
    retriever._save_bm25_index()

    progress_bar.progress(1.0)  # 100% complete

    total_time = time.time() - start_time
    status_text.text(f"✅ Indexing complete! Total time: {total_time:.2f} seconds")

    with details_container:
        st.write("---")
        st.success("🎉 **Indexing Complete!**")
        st.write(f"**Total processing time:** {total_time:.2f} seconds")
        st.write(f"**Average time per chunk:** {total_time/len(chunks):.3f} seconds")
        st.write(f"**Chunks per second:** {len(chunks)/total_time:.1f}")


def display_search_results(results, show_metadata=True):
    """Display search results in a nice format."""
    if not results:
        st.info("No results found.")
        return
    
    for i, result in enumerate(results, 1):
        with st.expander(f"Result {i} - Score: {result.score:.3f} ({result.search_type})"):
            st.write("**Message:**")
            st.write(result.text)
            
            if show_metadata and result.metadata:
                st.write("**Metadata:**")
                metadata_df = pd.DataFrame([result.metadata])
                st.dataframe(metadata_df, use_container_width=True)


def main():
    """Main Streamlit application."""
    st.set_page_config(
        page_title="ChatWhiz - Semantic Chat Search",
        page_icon="🔍",
        layout="wide"
    )
    
    st.title("🔍 ChatWhiz - Semantic Chat Search")
    st.markdown("*Search your chat history with AI-powered semantic understanding*")
    
    # Initialize system
    try:
        config, embedder, vector_store, retriever, rag_system = initialize_system()
    except Exception as e:
        st.error(f"Failed to initialize system: {e}")
        st.stop()
    
    # Sidebar for configuration
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        # Show current config
        st.subheader("Current Settings")
        st.write(f"**Model:** {config.get('embedding_model', 'N/A')}")
        st.write(f"**Chunk Size:** {config.get('chunk_size', 'N/A')}")
        st.write(f"**LLM Provider:** {config.get('llm_provider', 'none')}")
        
        # Show system stats
        st.subheader("📊 System Statistics")
        try:
            stats = retriever.get_stats()
            st.metric("Semantic Vectors", stats['semantic_vectors'])
            st.metric("BM25 Documents", stats['bm25_documents'])
            st.metric("Embedding Dimension", stats['embedding_dimension'])

            # Show model info
            st.subheader("🧠 Model Information")
            model_info = embedder.get_model_info()
            st.write(f"**Model:** {model_info['model_name']}")
            st.write(f"**Device:** {model_info['device']}")
            st.write(f"**Dimension:** {model_info['embedding_dimension']}")

            # Cache statistics
            if os.path.exists(config.get('cache_dir', 'data/cache')):
                cache_files = len([f for f in os.listdir(config.get('cache_dir', 'data/cache')) if f.endswith('.pkl')])
                st.metric("Cache Files", cache_files)

        except Exception as e:
            st.error(f"Error getting stats: {e}")
    
    # Main interface tabs
    tab1, tab2, tab3, tab4 = st.tabs(["🔍 Search", "📁 Upload & Index", "🧠 Encoding Monitor", "📊 Analytics"])
    
    with tab1:
        st.header("Search Your Chats")
        
        # Search interface
        col1, col2 = st.columns([3, 1])
        
        with col1:
            query = st.text_input(
                "Enter your search query:",
                placeholder="What did John say about the meeting?",
                help="Enter natural language queries to search your chat history"
            )
        
        with col2:
            search_mode = st.selectbox(
                "Search Mode:",
                ["semantic", "bm25", "hybrid"],
                help="Semantic: AI-powered similarity, BM25: Keyword matching, Hybrid: Combined approach"
            )
        
        # Search options
        col3, col4, col5 = st.columns(3)
        
        with col3:
            top_k = st.slider("Number of results:", 1, 20, 5)
        
        with col4:
            use_rag = st.checkbox(
                "Generate AI Response", 
                value=False,
                disabled=rag_system is None,
                help="Use LLM to generate a response based on search results"
            )
        
        with col5:
            show_metadata = st.checkbox("Show Metadata", value=True)
        
        # Search button
        if st.button("🔍 Search", type="primary") and query:
            # Create search progress container
            search_container = st.container()

            with search_container:
                search_progress = st.progress(0)
                search_status = st.empty()
                search_details = st.expander("🔍 Search Process Details", expanded=False)

            try:
                # Phase 1: Query encoding
                search_status.text("🧠 Encoding search query...")
                search_progress.progress(0.2)

                with search_details:
                    st.write("**🧠 Query Encoding Phase**")
                    st.write(f"Query: '{query}'")
                    st.write(f"Search mode: {search_mode}")

                # Phase 2: Searching
                search_status.text(f"🔍 Performing {search_mode} search...")
                search_progress.progress(0.6)

                results = retriever.search(
                    query=query,
                    mode=search_mode,
                    k=top_k,
                    threshold=config.get('similarity_threshold', 0.7)
                )

                search_progress.progress(1.0)
                search_status.text("✅ Search complete!")

                if results:
                    st.success(f"Found {len(results)} results")

                    # Display results
                    display_search_results(results, show_metadata)

                    # Generate RAG response if requested
                    if use_rag and rag_system:
                        st.markdown("---")
                        st.subheader("🤖 AI Response")

                        with st.spinner("Generating AI response..."):
                            rag_result = rag_system.generate_answer(query, results)

                            if 'error' in rag_result:
                                st.error(f"Error generating response: {rag_result['error']}")
                            else:
                                st.write(rag_result['answer'])

                                with st.expander("Response Details"):
                                    st.write(f"Context used: {rag_result['context_used']} messages")
                                    st.write(f"Prompt length: {rag_result.get('prompt_length', 'N/A')} characters")
                else:
                    st.info("No results found. Try adjusting your query or search mode.")

            except Exception as e:
                st.error(f"Search error: {e}")
    
    with tab2:
        st.header("Upload & Index Chat Files")
        
        # File upload
        uploaded_files = st.file_uploader(
            "Choose chat files to upload:",
            type=['txt', 'json', 'csv'],
            accept_multiple_files=True,
            help="Supported formats: WhatsApp exports (.txt), JSON chat logs, CSV files"
        )
        
        if uploaded_files:
            st.write(f"Selected {len(uploaded_files)} files:")
            for file in uploaded_files:
                st.write(f"- {file.name} ({file.size} bytes)")
            
            # Processing options
            col1, col2 = st.columns(2)
            
            with col1:
                chunk_strategy = st.selectbox(
                    "Chunking Strategy:",
                    ["sliding_window", "fixed", "conversation"],
                    help="How to split messages into chunks for embedding"
                )
            
            with col2:
                rebuild_index = st.checkbox(
                    "Rebuild Existing Index",
                    help="Clear existing data and rebuild from scratch"
                )
            
            # Process button
            if st.button("📁 Process & Index Files", type="primary"):
                # Update config with selected strategy
                config['chunk_strategy'] = chunk_strategy

                # Process files
                chunks = process_uploaded_files(uploaded_files, config)

                if chunks:
                    # Show encoding progress
                    st.markdown("---")
                    st.subheader("🧠 Encoding & Indexing Process")

                    # Create progress containers for encoding
                    encoding_progress = st.progress(0)
                    encoding_status = st.empty()
                    encoding_details = st.expander("🔍 Encoding Details", expanded=True)

                    try:
                        # Custom indexing with progress tracking
                        index_chunks_with_progress(
                            retriever, chunks, rebuild_index,
                            encoding_progress, encoding_status, encoding_details
                        )

                        st.success(f"🎉 Successfully indexed {len(chunks):,} chunks!")

                        # Show final stats
                        with encoding_details:
                            final_stats = retriever.get_stats()
                            st.write("📊 **Final Index Statistics:**")
                            st.write(f"- Semantic vectors: {final_stats['semantic_vectors']:,}")
                            st.write(f"- BM25 documents: {final_stats['bm25_documents']:,}")
                            st.write(f"- Embedding dimension: {final_stats['embedding_dimension']:,}")

                        # Clear cache to refresh stats
                        st.cache_resource.clear()
                        st.rerun()

                    except Exception as e:
                        st.error(f"❌ Indexing error: {e}")
                        import traceback
                        with encoding_details:
                            st.code(traceback.format_exc())
                else:
                    st.warning("⚠️ No chunks to index.")

    with tab3:
        st.header("🧠 Encoding Monitor")
        st.markdown("Monitor the embedding generation process and model performance.")

        # Model status
        col1, col2 = st.columns(2)

        with col1:
            st.subheader("🤖 Model Status")
            try:
                model_info = embedder.get_model_info()

                # Model info display
                st.write("**Current Model:**")
                st.code(f"""
Model: {model_info['model_name']}
Device: {model_info['device']}
Dimension: {model_info['embedding_dimension']}
Architecture: {model_info.get('transformer_architecture', 'N/A')}
Tokenizer: {model_info.get('tokenizer_type', 'N/A')}
                """)

                # Test encoding performance
                if st.button("🧪 Test Encoding Performance"):
                    test_texts = [
                        "Hello world",
                        "This is a test message",
                        "Testing the embedding model performance"
                    ]

                    with st.spinner("Testing encoding..."):
                        import time
                        start_time = time.time()
                        test_embeddings = embedder.encode(test_texts, use_cache=False)
                        end_time = time.time()

                        encoding_time = end_time - start_time

                        st.success("✅ Encoding test complete!")
                        st.write(f"**Time taken:** {encoding_time:.3f} seconds")
                        st.write(f"**Texts per second:** {len(test_texts)/encoding_time:.1f}")
                        st.write(f"**Average time per text:** {encoding_time/len(test_texts):.3f} seconds")
                        st.write(f"**Output shape:** {test_embeddings.shape}")

            except Exception as e:
                st.error(f"Error getting model info: {e}")

        with col2:
            st.subheader("📊 Cache Statistics")
            try:
                cache_dir = config.get('cache_dir', 'data/cache')
                if os.path.exists(cache_dir):
                    cache_files = [f for f in os.listdir(cache_dir) if f.endswith('.pkl')]

                    st.metric("Cache Files", len(cache_files))

                    if cache_files:
                        # Calculate cache size
                        total_size = sum(os.path.getsize(os.path.join(cache_dir, f)) for f in cache_files)
                        st.metric("Cache Size", f"{total_size / (1024*1024):.1f} MB")

                        # Show recent cache files
                        st.write("**Recent Cache Files:**")
                        recent_files = sorted(cache_files,
                                            key=lambda x: os.path.getmtime(os.path.join(cache_dir, x)),
                                            reverse=True)[:5]

                        for file in recent_files:
                            file_path = os.path.join(cache_dir, file)
                            file_size = os.path.getsize(file_path) / 1024  # KB
                            mod_time = os.path.getmtime(file_path)
                            st.write(f"- {file[:20]}... ({file_size:.1f} KB)")

                        # Clear cache button
                        if st.button("🗑️ Clear Cache"):
                            try:
                                embedder.clear_cache()
                                st.success("✅ Cache cleared!")
                                st.rerun()
                            except Exception as e:
                                st.error(f"Error clearing cache: {e}")
                    else:
                        st.info("No cache files found")
                else:
                    st.warning("Cache directory not found")

            except Exception as e:
                st.error(f"Error reading cache: {e}")

        # Live encoding demo
        st.subheader("🎯 Live Encoding Demo")
        st.markdown("Enter text below to see real-time encoding in action:")

        demo_text = st.text_area(
            "Enter text to encode:",
            placeholder="Type your message here to see how it gets encoded...",
            height=100
        )

        if demo_text and st.button("🧠 Encode Text"):
            with st.spinner("Encoding..."):
                import time
                start_time = time.time()

                # Create progress bar for demo
                demo_progress = st.progress(0)
                demo_status = st.empty()

                demo_status.text("🧠 Generating embedding...")
                demo_progress.progress(0.5)

                embedding = embedder.encode([demo_text], use_cache=False)

                demo_progress.progress(1.0)
                end_time = time.time()

                demo_status.text("✅ Encoding complete!")

                # Show results
                col1, col2 = st.columns(2)

                with col1:
                    st.write("**Input Text:**")
                    st.code(demo_text)
                    st.write(f"**Encoding Time:** {end_time - start_time:.3f} seconds")
                    st.write(f"**Text Length:** {len(demo_text)} characters")

                with col2:
                    st.write("**Embedding Vector:**")
                    st.write(f"**Shape:** {embedding.shape}")
                    st.write(f"**First 10 values:**")
                    st.code(str(embedding[0][:10]))

                    # Show embedding statistics
                    import numpy as np
                    st.write(f"**Mean:** {np.mean(embedding):.4f}")
                    st.write(f"**Std:** {np.std(embedding):.4f}")
                    st.write(f"**Min:** {np.min(embedding):.4f}")
                    st.write(f"**Max:** {np.max(embedding):.4f}")

        # Similarity comparison demo
        st.subheader("🔗 Similarity Comparison Demo")
        st.markdown("Compare the similarity between different texts:")

        col1, col2 = st.columns(2)

        with col1:
            text1 = st.text_input("Text 1:", placeholder="Enter first text...")

        with col2:
            text2 = st.text_input("Text 2:", placeholder="Enter second text...")

        if text1 and text2 and st.button("🔗 Compare Similarity"):
            with st.spinner("Computing similarity..."):
                # Encode both texts
                embeddings = embedder.encode([text1, text2], use_cache=False)

                # Calculate cosine similarity
                from sklearn.metrics.pairwise import cosine_similarity
                import numpy as np

                similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]

                # Display results
                st.write("**Similarity Results:**")

                # Create a visual similarity meter
                similarity_percentage = (similarity + 1) / 2 * 100  # Convert from [-1,1] to [0,100]

                col1, col2, col3 = st.columns([1, 2, 1])

                with col2:
                    st.metric("Cosine Similarity", f"{similarity:.4f}")
                    st.progress(similarity_percentage / 100)

                    if similarity > 0.8:
                        st.success("🟢 Very Similar")
                    elif similarity > 0.6:
                        st.info("🟡 Moderately Similar")
                    elif similarity > 0.3:
                        st.warning("🟠 Somewhat Similar")
                    else:
                        st.error("🔴 Not Similar")

                # Show detailed comparison
                with st.expander("📊 Detailed Comparison"):
                    st.write("**Text 1:**")
                    st.code(text1)
                    st.write("**Text 2:**")
                    st.code(text2)
                    st.write(f"**Similarity Score:** {similarity:.6f}")
                    st.write(f"**Similarity Percentage:** {similarity_percentage:.2f}%")

    with tab4:
        st.header("Analytics & Insights")
        
        try:
            stats = retriever.get_stats()
            
            if stats['semantic_vectors'] > 0 or stats['bm25_documents'] > 0:
                # Display metrics
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("Total Vectors", stats['semantic_vectors'])
                
                with col2:
                    st.metric("BM25 Documents", stats['bm25_documents'])
                
                with col3:
                    st.metric("Embedding Dim", stats['embedding_dimension'])
                
                with col4:
                    st.metric("Index Type", stats['index_type'])
                
                # System status
                st.subheader("System Status")
                
                status_data = {
                    "Component": ["Embedder", "Vector Store", "BM25 Index", "LLM Provider"],
                    "Status": [
                        "✅ Ready" if embedder else "❌ Not Available",
                        "✅ Ready" if stats['semantic_vectors'] > 0 else "⚠️ Empty",
                        "✅ Ready" if stats['bm25_documents'] > 0 else "⚠️ Empty",
                        "✅ Available" if rag_system and rag_system.llm_provider.is_available() else "❌ Not Available"
                    ]
                }
                
                st.dataframe(pd.DataFrame(status_data), use_container_width=True)
                
                # Configuration display
                st.subheader("Current Configuration")
                config_df = pd.DataFrame([
                    {"Setting": k, "Value": str(v)} 
                    for k, v in config.items()
                ])
                st.dataframe(config_df, use_container_width=True)
                
            else:
                st.info("No data indexed yet. Upload some chat files to see analytics.")
        
        except Exception as e:
            st.error(f"Error loading analytics: {e}")
    
    # Footer
    st.markdown("---")
    st.markdown(
        "Built with ❤️ using [Instructor-XL](https://huggingface.co/hkunlp/instructor-xl) "
        "and [Streamlit](https://streamlit.io/)"
    )


if __name__ == "__main__":
    main()
